import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/modules/report/mixin/report_mixin.dart';
import 'package:koc_app/src/shared/widgets/charts/chart_constants.dart';

/// Configuration for chart empty state appearance
class ChartEmptyStateConfig {
  const ChartEmptyStateConfig({
    this.maxValue = 500.0,
    this.interval = 100.0,
    this.message = 'No data available',
    this.showGrid = true,
    this.showLegend = false,
    this.showTitle = false,
    this.title = '',
    this.backgroundColor,
    this.borderColor,
    this.padding,
    this.margin,
    this.aspectRatio = 1.5,
    this.chartType = ChartEmptyStateType.lineChart,
    this.gridLineCount = 5,
    this.titleStyle,
    this.labelStyle,
  });

  final double maxValue;
  final double interval;
  final String message;
  final bool showGrid;
  final bool showLegend;
  final bool showTitle;
  final String title;
  final Color? backgroundColor;
  final Color? borderColor;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final double aspectRatio;
  final ChartEmptyStateType chartType;
  final int gridLineCount;
  final TextStyle? titleStyle;
  final TextStyle? labelStyle;
}

/// Type of chart for empty state rendering
enum ChartEmptyStateType {
  lineChart,
  horizontalBarChart,
}

/// Shared builder for chart empty states
class ChartEmptyStateBuilder with ReportMixin {
  const ChartEmptyStateBuilder();

  /// Build a unified no data widget for charts
  Widget buildNoDataWidget(
    BuildContext context,
    ChartEmptyStateConfig config, {
    Widget Function(BuildContext)? legendBuilder,
    Widget Function(BuildContext)? titleBuilder,
  }) {
    Widget content = Column(
      children: [
        if (config.showTitle && titleBuilder != null) titleBuilder(context),
        if (config.showLegend && legendBuilder != null) legendBuilder(context),
        if (config.showLegend && legendBuilder != null) SizedBox(height: 16.r),
        Expanded(
          child: _buildChartContent(context, config),
        ),
      ],
    );

    // For line charts, wrap in container with border
    if (config.chartType == ChartEmptyStateType.lineChart) {
      return Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: config.borderColor ?? const Color(0xFFE7E7E7),
            width: 1.r,
          ),
          color: config.backgroundColor,
        ),
        padding: config.padding ?? EdgeInsets.all(12.r),
        margin: config.margin,
        child: content,
      );
    }

    return content;
  }

  Widget _buildChartContent(BuildContext context, ChartEmptyStateConfig config) {
    switch (config.chartType) {
      case ChartEmptyStateType.lineChart:
        return _buildLineChartEmptyState(context, config);
      case ChartEmptyStateType.horizontalBarChart:
        return _buildHorizontalBarChartEmptyState(context, config);
    }
  }

  Widget _buildLineChartEmptyState(BuildContext context, ChartEmptyStateConfig config) {
    return Stack(
      children: [
        LineChart(
          LineChartData(
            gridData: _buildGridData(config),
            titlesData: _buildEmptyStateTitlesData(context, config),
            borderData: FlBorderData(show: ChartConstants.showBorder),
            minY: 0,
            maxY: config.maxValue,
            lineBarsData: [],
          ),
        ),
        Center(
          child: Container(
            padding: EdgeInsets.all(16.r),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.9),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.bar_chart_outlined, size: 32.r, color: Colors.grey[400]),
                SizedBox(height: 8.r),
                Text(
                  config.message,
                  style: config.labelStyle ??
                      Theme.of(context).textTheme.labelMedium?.copyWith(
                            color: Colors.grey[600],
                            fontSize: 12.sp,
                          ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildHorizontalBarChartEmptyState(BuildContext context, ChartEmptyStateConfig config) {
    return Padding(
      padding: EdgeInsets.only(right: 12.r, top: 12.r, bottom: 8.r),
      child: AspectRatio(
        aspectRatio: config.aspectRatio,
        child: Column(
          children: [
            Expanded(
              child: Stack(
                children: [
                  if (config.showGrid)
                    Positioned(
                      left: 88.r,
                      right: 0,
                      top: 0,
                      bottom: 0,
                      child: CustomPaint(
                        painter: GridLinePainter(
                          verticalLineCount: config.gridLineCount,
                          horizontalLineCount: config.gridLineCount,
                          color: Colors.grey[300]!,
                          strokeWidth: 0.8,
                        ),
                      ),
                    ),
                  Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.bar_chart_outlined, size: 32.r, color: Colors.grey[400]),
                        SizedBox(height: 4.r),
                        Text(
                          config.message,
                          style: config.titleStyle ??
                              Theme.of(context).textTheme.labelMedium?.copyWith(
                                    color: Colors.grey[600],
                                    fontSize: 12.sp,
                                  ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 8.r),
            _buildAxisLabels(context, config),
          ],
        ),
      ),
    );
  }

  Widget _buildAxisLabels(BuildContext context, ChartEmptyStateConfig config) {
    return Row(
      children: [
        SizedBox(width: 80.r),
        Expanded(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: List.generate(6, (index) {
              final value = (config.maxValue / 5) * index;
              return Text(
                formatChartNumber(value),
                style: config.labelStyle ??
                    Theme.of(context).textTheme.labelSmall?.copyWith(
                          fontSize: 10.sp,
                          color: Colors.grey[500],
                        ),
              );
            }),
          ),
        ),
      ],
    );
  }

  FlGridData _buildGridData(ChartEmptyStateConfig config) {
    return FlGridData(
      show: config.showGrid,
      drawVerticalLine: false,
      horizontalInterval: config.interval,
      getDrawingHorizontalLine: (value) {
        return FlLine(
          color: Colors.grey[300]!,
          strokeWidth: 0.5,
        );
      },
    );
  }

  FlTitlesData _buildEmptyStateTitlesData(BuildContext context, ChartEmptyStateConfig config) {
    return FlTitlesData(
      leftTitles: AxisTitles(
        sideTitles: SideTitles(
          showTitles: true,
          reservedSize: 40.r,
          getTitlesWidget: (value, meta) {
            return Text(
              formatChartNumber(value),
              style: config.labelStyle ??
                  Theme.of(context).textTheme.labelSmall?.copyWith(
                        fontSize: 10.sp,
                        color: Colors.grey[500],
                      ),
            );
          },
        ),
      ),
      bottomTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
      rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
      topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
    );
  }
}

/// Custom painter for grid lines in horizontal bar charts
class GridLinePainter extends CustomPainter {
  final int verticalLineCount;
  final int horizontalLineCount;
  final Color color;
  final double strokeWidth;

  const GridLinePainter({
    required this.verticalLineCount,
    required this.horizontalLineCount,
    required this.color,
    required this.strokeWidth,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth;

    // Draw vertical lines
    for (int i = 0; i <= verticalLineCount; i++) {
      final x = (size.width / verticalLineCount) * i;
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
    }

    // Draw horizontal lines
    for (int i = 0; i <= horizontalLineCount; i++) {
      final y = (size.height / horizontalLineCount) * i;
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
