import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// Simple configuration for chart empty state
class ChartEmptyStateConfig {
  const ChartEmptyStateConfig({
    this.message = 'There is no data available at the moment',
  });

  final String message;
}

/// Shared builder for chart empty states
class ChartEmptyStateBuilder {
  const ChartEmptyStateBuilder();

  /// Build a simple no data widget with icon and text
  Widget buildNoDataWidget(
    BuildContext context, {
    String? message,
    Widget? title,
    Color? backgroundColor,
    Color? borderColor,
    EdgeInsets? padding,
    EdgeInsets? margin,
  }) {
    Widget content = Column(
      children: [
        if (title != null) title,
        Expanded(
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Chart icon with orange color like in the image
                Icon(
                  Icons.trending_up,
                  size: 48.r,
                  color: const Color(0xFFFFB522), // Orange color
                ),
                SizedBox(height: 16.r),
                Text(
                  message ?? 'There is no data available at the moment',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey[600],
                        fontSize: 14.sp,
                      ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ],
    );

    // Wrap with container that has border
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: borderColor ?? const Color(0xFFE7E7E7),
          width: 1.r,
        ),
        color: backgroundColor,
      ),
      padding: padding ?? EdgeInsets.all(12.r),
      margin: margin,
      child: content,
    );
  }
}
