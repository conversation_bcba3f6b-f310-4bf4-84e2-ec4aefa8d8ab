import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/shared/widgets/charts/chart_empty_state_builder.dart';

/// Example usage of the simplified chart empty state
class ChartEmptyStateExample extends StatelessWidget {
  const ChartEmptyStateExample({super.key});

  @override
  Widget build(BuildContext context) {
    const emptyStateBuilder = ChartEmptyStateBuilder();

    return Scaffold(
      appBar: AppBar(
        title: const Text('Chart Empty State Example'),
      ),
      body: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          children: [
            // Example 1: Simple empty state without title
            SizedBox(
              height: 200.r,
              child: emptyStateBuilder.buildNoDataWidget(
                context,
                message: 'No data available',
              ),
            ),

            SizedBox(height: 24.r),

            // Example 2: Empty state with title
            SizedBox(
              height: 250.r,
              child: emptyStateBuilder.buildNoDataWidget(
                context,
                message: 'There is no data available at the moment',
                title: Padding(
                  padding: EdgeInsets.only(top: 12.r, bottom: 8.r),
                  child: Text(
                    'Clicks vs Conversions',
                    style: Theme.of(context).textTheme.labelMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
