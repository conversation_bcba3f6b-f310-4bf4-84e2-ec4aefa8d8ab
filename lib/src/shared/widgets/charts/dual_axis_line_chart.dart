import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/modules/report/mixin/report_mixin.dart';
import 'package:koc_app/src/shared/widgets/charts/chart_constants.dart';
import 'package:koc_app/src/shared/widgets/charts/chart_models.dart';
import 'package:koc_app/src/shared/widgets/charts/chart_empty_state_builder.dart';

/// A reusable dual-axis line chart widget with consistent styling across the app
class DualAxisLineChart extends StatelessWidget with ReportMixin {
  const DualAxisLineChart({
    super.key,
    required this.data,
    this.config = const DualAxisChartConfig(),
    this.height,
    this.showNoDataMessage = true,
  });

  final DualAxisChartData data;
  final DualAxisChartConfig config;
  final double? height;
  final bool showNoDataMessage;

  @override
  Widget build(BuildContext context) {
    final bool hasData = data.leftAxisData.any((value) => value > 0) || data.rightAxisData.any((value) => value > 0);

    if (!hasData && showNoDataMessage) {
      return _buildNoDataWidget(context);
    }

    final scaling = calculateDualAxisScaling(data.leftAxisData, data.rightAxisData, intervals: config.yAxisGridLines);

    Widget chartContent = Column(
      children: [
        if (config.showLegend && config.legendPosition == LegendPosition.above) _buildLegend(context),
        if (config.showLegend && config.legendPosition == LegendPosition.above) SizedBox(height: 8.r),
        Expanded(
          child: _buildChart(context, scaling),
        ),
        if (config.showLegend && config.legendPosition == LegendPosition.below) SizedBox(height: 8.r),
        if (config.showLegend && config.legendPosition == LegendPosition.below) _buildLegend(context),
      ],
    );

    if (height != null) {
      return SizedBox(
        height: height,
        child: chartContent,
      );
    }

    return chartContent;
  }

  Widget _buildNoDataWidget(BuildContext context) {
    const emptyStateBuilder = ChartEmptyStateBuilder();

    return emptyStateBuilder.buildNoDataWidget(
      context,
      message: 'No data available',
      backgroundColor: Colors.white,
      borderColor: const Color(0xFFE7E7E7),
    );
  }

  Widget _buildLegend(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildLegendItem(
          context,
          _cleanLabelText(data.leftAxisLabel),
          config.leftAxisColor ?? ChartConstants.leftLineColor,
        ),
        SizedBox(width: 24.r),
        _buildLegendItem(
          context,
          _cleanLabelText(data.rightAxisLabel),
          config.rightAxisColor ?? ChartConstants.rightLineColor,
        ),
      ],
    );
  }

  String _cleanLabelText(String label) {
    return label.replaceAll('&', '').trim();
  }

  Widget _buildLegendItem(BuildContext context, String label, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 8.r,
          height: 8.r,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        SizedBox(width: 8.r),
        Text(
          label,
          style: Theme.of(context).textTheme.labelMedium?.copyWith(
                color: Colors.grey[700],
              ),
        ),
      ],
    );
  }

  Widget _buildChart(BuildContext context, ChartScaling scaling) {
    return LineChart(
      LineChartData(
        lineTouchData: _buildTouchData(scaling),
        gridData: _buildGridData(scaling),
        titlesData: _buildTitlesData(scaling),
        borderData: FlBorderData(show: ChartConstants.showBorder),
        minY: 0,
        maxY: scaling.leftMaxY,
        lineBarsData: _buildLineBarsData(scaling),
      ),
    );
  }

  LineTouchData _buildTouchData(ChartScaling scaling) {
    return LineTouchData(
      enabled: ChartConstants.enableTouch,
      touchTooltipData: LineTouchTooltipData(
        getTooltipColor: (touchedSpot) => config.tooltipBackgroundColor ?? ChartConstants.tooltipBackgroundColor,
        getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
          return touchedBarSpots.map((barSpot) {
            final isLeftAxis = barSpot.barIndex == 0;
            final color = isLeftAxis
                ? (config.leftAxisColor ?? ChartConstants.leftLineColor)
                : (config.rightAxisColor ?? ChartConstants.rightLineColor);

            String value;
            if (isLeftAxis) {
              value = formatTooltipNumber(barSpot.y, data.leftAxisLabel);
            } else {
              final originalValue = convertScaledValueToOriginal(
                barSpot.y,
                scaling.rightMaxY,
                scaling.leftMaxY,
              );
              value = formatTooltipNumber(originalValue, data.rightAxisLabel);
            }

            final label = isLeftAxis ? data.leftAxisLabel : data.rightAxisLabel;

            return LineTooltipItem(
              '$label: $value',
              TextStyle(
                color: color,
                fontWeight: FontWeight.bold,
                fontSize: 12.sp,
              ),
            );
          }).toList();
        },
      ),
    );
  }

  FlGridData _buildGridData(ChartScaling scaling) {
    return FlGridData(
      show: config.showGrid,
      drawVerticalLine: false,
      horizontalInterval: scaling.leftInterval,
      getDrawingHorizontalLine: (value) {
        return FlLine(
          color: Colors.grey[300]!,
          strokeWidth: 0.5,
        );
      },
    );
  }

  FlTitlesData _buildTitlesData(ChartScaling scaling) {
    return FlTitlesData(
      leftTitles: AxisTitles(
        sideTitles: SideTitles(
          showTitles: true,
          reservedSize: 35.r,
          interval: scaling.leftInterval,
          getTitlesWidget: (value, meta) {
            return Text(
              formatChartNumber(value),
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 10.sp,
              ),
            );
          },
        ),
      ),
      rightTitles: AxisTitles(
        sideTitles: SideTitles(
          showTitles: true,
          reservedSize: 35.r,
          interval: scaling.leftInterval,
          getTitlesWidget: (value, meta) {
            final rightValue = convertScaledValueToOriginal(
              value,
              scaling.rightMaxY,
              scaling.leftMaxY,
            );
            return Padding(
              padding: EdgeInsets.only(left: 8.r),
              child: Text(
                formatChartNumber(rightValue),
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 10.sp,
                ),
                textAlign: TextAlign.left,
              ),
            );
          },
        ),
      ),
      bottomTitles: AxisTitles(
        sideTitles: SideTitles(
          showTitles: true,
          reservedSize: 35.r,
          interval: 1,
          getTitlesWidget: (value, meta) {
            final index = value.toInt();
            if (index >= 0 && index < data.labels.length) {
              if (index % config.xAxisLabelInterval == 0) {
                return Transform.translate(
                  offset: Offset(-12.r, 0),
                  child: Padding(
                    padding: EdgeInsets.only(top: 16.r),
                    child: Transform.rotate(
                      angle: ChartConstants.xAxisLabelRotation,
                      alignment: Alignment.topCenter,
                      child: Text(
                        data.labels[index],
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 8.sp,
                        ),
                        textAlign: TextAlign.center,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ),
                  ),
                );
              }
            }
            return const SizedBox.shrink();
          },
        ),
      ),
      topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
    );
  }

  List<LineChartBarData> _buildLineBarsData(ChartScaling scaling) {
    return [
      LineChartBarData(
        spots: List.generate(data.leftAxisData.length, (index) {
          return FlSpot(index.toDouble(), data.leftAxisData[index].toDouble());
        }),
        isCurved: ChartConstants.showCurvedLines,
        color: config.leftAxisColor ?? ChartConstants.leftLineColor,
        barWidth: ChartConstants.lineWidth,
        dotData: FlDotData(
          show: ChartConstants.showDots,
          getDotPainter: (spot, percent, barData, index) {
            return FlDotCirclePainter(
              radius: ChartConstants.dotRadius,
              color: config.leftAxisColor ?? ChartConstants.leftLineColor,
              strokeWidth: ChartConstants.dotStrokeWidth,
              strokeColor: ChartConstants.dotStrokeColor,
            );
          },
        ),
        belowBarData: BarAreaData(
          show: ChartConstants.showAreaFill,
          color:
              (config.leftAxisColor ?? ChartConstants.leftLineColor).withValues(alpha: ChartConstants.areaFillOpacity),
        ),
      ),
      LineChartBarData(
        spots: List.generate(data.rightAxisData.length, (index) {
          final scaledValue = scaleValueForDualAxis(
            data.rightAxisData[index],
            scaling.rightMaxY,
            scaling.leftMaxY,
          );
          return FlSpot(index.toDouble(), scaledValue);
        }),
        isCurved: ChartConstants.showCurvedLines,
        color: config.rightAxisColor ?? ChartConstants.rightLineColor,
        barWidth: ChartConstants.lineWidth,
        dotData: FlDotData(
          show: ChartConstants.showDots,
          getDotPainter: (spot, percent, barData, index) {
            return FlDotCirclePainter(
              radius: ChartConstants.dotRadius,
              color: config.rightAxisColor ?? ChartConstants.rightLineColor,
              strokeWidth: ChartConstants.dotStrokeWidth,
              strokeColor: ChartConstants.dotStrokeColor,
            );
          },
        ),
        belowBarData: BarAreaData(
          show: ChartConstants.showAreaFill,
          color: (config.rightAxisColor ?? ChartConstants.rightLineColor)
              .withValues(alpha: ChartConstants.areaFillOpacity),
        ),
      ),
    ];
  }
}
